<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>com.ctrip</groupId>
		<artifactId>super-pom</artifactId>
		<version>1.0.9</version>
	</parent>
	<groupId>com.ctrip.mit.mitexam</groupId>
	<artifactId>foobar</artifactId>
	<version>0.0.1-SNAPSHOT</version>
	<packaging>war</packaging>
	<name>foobar</name>
	<description>100273621567</description>
	<properties>
		<framework-bom.version>8.33.3-Java21</framework-bom.version>
		<java.version>21</java.version>
		<project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
		<project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
		<releases.repo>http://maven.release.ctripcorp.com/nexus/content/repositories/mitrelease</releases.repo>
		<log4j2.version>2.20.0</log4j2.version>
	</properties>
	<dependencies>
		<dependency>
			<groupId>org.mybatis.spring.boot</groupId>
			<artifactId>mybatis-spring-boot-starter</artifactId>
			<version>1.3.0</version>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-web</artifactId>
		</dependency>
		<dependency>
			<groupId>com.ctrip.framework</groupId>
			<artifactId>fx-spring-boot-starter-credis</artifactId>
		</dependency>
		<dependency>
			<groupId>com.ctrip.framework</groupId>
			<artifactId>fx-spring-boot-starter-dal</artifactId>
		</dependency>
		<dependency>
			<groupId>com.ctrip.framework</groupId>
			<artifactId>fx-spring-boot-starter-monitor</artifactId>
		</dependency>
		<dependency>
			<groupId>com.ctrip.framework</groupId>
			<artifactId>fx-spring-boot-starter-qconfig</artifactId>
		</dependency>
		<dependency>
			<groupId>com.ctrip.platform</groupId>
			<artifactId>ctrip-datasource</artifactId>
			<exclusions>
				<exclusion>
					<groupId>org.apache.tomcat</groupId>
					<artifactId>tomcat-juli</artifactId>
				</exclusion>
			</exclusions>
		</dependency>

		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-test</artifactId>
			<scope>test</scope>
		</dependency>
	</dependencies>
	<dependencyManagement>
		<dependencies>
			<dependency>
				<groupId>com.ctrip.framework</groupId>
				<artifactId>framework-bom</artifactId>
				<version>${framework-bom.version}</version>
				<type>pom</type>
				<scope>import</scope>
			</dependency>
		</dependencies>
	</dependencyManagement>

	<build>
		<plugins>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-compiler-plugin</artifactId>
				<version>3.11.0</version>
				<configuration>
					<source>21</source>
					<target>21</target>
				</configuration>
			</plugin>
			<plugin>
				<groupId>org.jacoco</groupId>
				<artifactId>jacoco-maven-plugin</artifactId>
				<version>0.8.11</version>
				<executions>
					<execution>
						<id>default-instrument</id>
						<goals>
							<goal>instrument</goal>
						</goals>
					</execution>
					<execution>
						<id>default-restore-instrumented-classes</id>
						<goals>
							<goal>restore-instrumented-classes</goal>
						</goals>
					</execution>
				</executions>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-surefire-plugin</artifactId>
				<!--离线模式必需指定， 否则到模块根目录而不是target目录了-->
				<configuration>
					<systemPropertyVariables>
						<jacoco-agent.destfile>target/jacoco.exec</jacoco-agent.destfile>
					</systemPropertyVariables>
				</configuration>
			</plugin>
			<plugin>
				<groupId>org.codehaus.gmavenplus</groupId>
				<artifactId>gmavenplus-plugin</artifactId>
				<version>1.6</version>
				<executions>
					<execution>
						<goals>
							<goal>compile</goal>
							<goal>compileTests</goal>
						</goals>
					</execution>
				</executions>
			</plugin>
			<plugin>
				<groupId>com.ctrip.ibu.platform</groupId>
				<artifactId>shark-maven-plugin</artifactId>
				<version>1.1.1</version>
				<executions>
					<execution>
						<goals>
							<goal>pack-download</goal>
						</goals>

					</execution>
				</executions>
			</plugin>
		</plugins>
	</build>

</project>
