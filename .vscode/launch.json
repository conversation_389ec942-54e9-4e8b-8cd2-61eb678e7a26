{"version": "0.2.0", "configurations": [{"type": "java", "name": "AppSpringBootConfiguration", "request": "launch", "mainClass": "com.ctrip.mit.mitexam.foobar.AppSpringBootConfiguration", "projectName": "foobar", "vmArgs": "-Djdk.attach.allowAttachSelf=true -XX:+EnableDynamicAgentLoading --add-opens java.management/sun.management=ALL-UNNAMED --add-opens java.base/java.lang=ALL-UNNAMED --add-opens java.base/java.lang.reflect=ALL-UNNAMED --add-opens java.base/sun.reflect.annotation=ALL-UNNAMED --add-opens java.base/java.math=ALL-UNNAMED --add-opens java.base/java.util=ALL-UNNAMED --add-opens java.base/sun.util.calendar=ALL-UNNAMED --add-opens java.base/sun.util.calendar=ALL-UNNAMED --add-opens java.base/java.io=ALL-UNNAMED --add-opens java.base/java.net=ALL-UNNAMED --add-opens java.xml/com.sun.org.apache.xerces.internal.jaxp.datatype=ALL-UNNAMED"}]}