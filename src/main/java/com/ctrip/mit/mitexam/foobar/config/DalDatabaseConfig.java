package com.ctrip.mit.mitexam.foobar.config;

@Configuration
@EnableDalMybatis
@MapperScan("com.ctrip.mitexam.mapper")public class DalDatabaseConfig {
    private DalDataSourceFactory dalDataSourceFactory = new DalDataSourceFactory();
    private final String dalCluster = "mitexamdb_dalcluster";
    @Bean
    public DataSource dataSource() throws Exception{ return dalDataSourceFactory.getOrCreateDataSource(dalCluster); }
    @Bean
    public DataSourceTransactionManager transactionManager() throws Exception{ return new DataSourceTransactionManager(dataSource());
    }
    @Bean
    public SqlSessionFactoryBean sqlSessionFactoryBean(ResourceLoader resourceLoader) throws Exception { SqlSessionFactoryBean sqlSessionFactoryBean = new SqlSessionFactoryBean(); sqlSessionFactoryBean.setDataSource(dataSource()); sqlSessionFactoryBean.setConfigLocation(resourceLoader.getResource("classpath:mybatis-config.xml")); return sqlSessionFactoryBean;
    }
}