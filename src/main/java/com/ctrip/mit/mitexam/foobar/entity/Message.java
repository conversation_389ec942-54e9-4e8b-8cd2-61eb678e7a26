package com.ctrip.mit.mitexam.foobar.entity;

import java.sql.Timestamp;

public class Message { private long id; private String app_id; private long timestamp; private String content; private Timestamp DataChange_LastTime;
    @Override
    public String toString() { return "Message{" + "id=" + id +
            ", app_id='" + app_id + '\'' +
            ", timestamp=" + timestamp +
            ", content='" + content + '\'' +
            ", DataChange_LastTime=" + DataChange_LastTime +
            '}';
    }
    public long getId() { return id;
    }
    public void setId(long id) { this.id = id;
    }
    public String getapp_id() { return app_id;
    }
    public void setapp_id(String app_id) { this.app_id = app_id;
    }
    public long getTimestamp() { return timestamp;
    }
    public void setTimestamp(long timestamp) { this.timestamp = timestamp;
    }
    public String getContent() { return content;
    }
    public void setContent(String content) { this.content = content;
    }
    public Timestamp getDataChange_LastTime() { return DataChange_LastTime;
    }
    public void setDataChange_LastTime(Timestamp DataChange_LastTime) { this.DataChange_LastTime = DataChange_LastTime;
    }
}
